2025-06-21 19:59:28 - app.core.logging - INFO - logging - setup_logging - 92 - Logging configured successfully
2025-06-21 19:59:28 - uvicorn.error - INFO - server - serve - 76 - Started server process [1248]
2025-06-21 19:59:28 - uvicorn.error - INFO - on - startup - 46 - Waiting for application startup.
2025-06-21 19:59:28 - app.main - INFO - main - lifespan - 36 - Starting up FastAPI User Management API
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - 2689 - BEGIN (implicit)
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - PRAGMA main.table_info("users")
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - [raw sql] ()
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - PRAGMA temp.table_info("users")
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - [raw sql] ()
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - 
CREATE TABLE users (
	id INTEGER NOT NULL, 
	username VARCHAR(50) NOT NULL, 
	email VARCHAR(100) NOT NULL, 
	password_hash VARCHAR(255) NOT NULL, 
	role VARCHAR(5) NOT NULL, 
	is_active BOOLEAN NOT NULL, 
	created_at DATETIME DEFAULT (CURRENT_TIMESTAMP) NOT NULL, 
	updated_at DATETIME DEFAULT (CURRENT_TIMESTAMP) NOT NULL, 
	PRIMARY KEY (id)
)


2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - [no key 0.00058s] ()
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - CREATE UNIQUE INDEX ix_users_username ON users (username)
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - [no key 0.00028s] ()
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - CREATE INDEX ix_users_id ON users (id)
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - [no key 0.00046s] ()
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - CREATE UNIQUE INDEX ix_users_email ON users (email)
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - [no key 0.00023s] ()
2025-06-21 19:59:28 - app.db.database - INFO - database - create_tables - 50 - Database tables created successfully
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _connection_commit_impl - 2695 - COMMIT
2025-06-21 19:59:28 - app.core.init_db - INFO - init_db - init_database - 20 - Database tables created successfully
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - 2689 - BEGIN (implicit)
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - SELECT users.id, users.username, users.email, users.password_hash, users.role, users.is_active, users.created_at, users.updated_at 
FROM users 
WHERE users.username = ?
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - [generated in 0.00054s] ('admin',)
2025-06-21 19:59:28 - passlib.handlers.bcrypt - WARNING - bcrypt - _load_backend_mixin - 622 - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - INSERT INTO users (username, email, password_hash, role, is_active) VALUES (?, ?, ?, ?, ?) RETURNING id, created_at, updated_at
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _execute_context - 1848 - [generated in 0.00040s] ('admin', '<EMAIL>', '$2b$12$0Mvfj3S/5zgaJlkaEoZyAOS2a3V3QunDhFh2uBQlzSatxBP3g2j8u', 'ADMIN', 1)
2025-06-21 19:59:28 - sqlalchemy.engine.Engine - INFO - base - _connection_commit_impl - 2695 - COMMIT
2025-06-21 19:59:28 - app.core.init_db - INFO - init_db - create_default_admin - 58 - Default admin user created: admin
2025-06-21 19:59:28 - app.main - INFO - main - lifespan - 39 - Database initialized successfully
2025-06-21 19:59:28 - uvicorn.error - INFO - on - startup - 60 - Application startup complete.
2025-06-21 19:59:39 - uvicorn.error - INFO - server - shutdown - 264 - Shutting down
2025-06-21 19:59:39 - uvicorn.error - INFO - on - shutdown - 65 - Waiting for application shutdown.
2025-06-21 19:59:39 - app.main - INFO - main - lifespan - 47 - Shutting down FastAPI User Management API
2025-06-21 19:59:39 - uvicorn.error - INFO - on - shutdown - 76 - Application shutdown complete.
2025-06-21 19:59:39 - uvicorn.error - INFO - server - serve - 86 - Finished server process [1248]
